import React, { useState, useEffect } from 'react';
import OptimizedYouTubeEmbed from '../common/OptimizedYouTubeEmbed';
import { useResponsiveImage, IMAGE_CATEGORIES } from '../../hooks/useResponsiveImage';

// Import icons and logos (keeping these for now, will update later)
import locationIcon from '../../assets/locationicon.png';
import instrumentIcon from '../../assets/instrument.png';
import maintitlem from '../../assets/image-layouts/maintitlem.png';
import maintitlel from '../../assets/image-layouts/maintitlel.png';

import './Homepage.css';

const Homepage = ({ language = 'english' }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Function to get responsive logo based on screen size
  const getResponsiveLogo = () => {
    if (window.innerWidth >= 1920) {
      return maintitlem; // 24-inch monitors
    } else if (window.innerWidth >= 769) {
      return maintitlel; // Laptops
    } else {
      return maintitlem; // Mobile (using same as monitor for now)
    }
  };

  const [currentLogo, setCurrentLogo] = useState(getResponsiveLogo());
  const [secondsVisible, setSecondsVisible] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  // Removed video state management - now handled by OptimizedYouTubeEmbed

  // Responsive gallery images
  const cyclingImage = useResponsiveImage('cycling.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/cycling.jpg');
  const jumpingImage = useResponsiveImage('jumping.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/jumping.jpg');
  const karthickImage = useResponsiveImage('karthick.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/karthick.jpg');
  const karupannanImage = useResponsiveImage('karupannan.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/karupannan.jpg');
  const kuththattamImage = useResponsiveImage('kuththattam.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/kuththattam.jpg');
  const melamImage = useResponsiveImage('melam.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/melam.jpg');
  const poikaalkuthiraiImage = useResponsiveImage('poikaalkuthirai.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/poikaalkuthirai.jpg');
  const sakthiImage = useResponsiveImage('sakthi.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/sakthi.jpg');
  const uralImage = useResponsiveImage('ural.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/ural.jpg');
  const uriImage = useResponsiveImage('uri.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/uri.jpg');
  const uriyadiImage = useResponsiveImage('uriyadi.jpg', IMAGE_CATEGORIES.GALLERY, 'homepage/uriyadi.jpg');

  // Responsive snapshot images
  const snapshot30kFootfall = useResponsiveImage('30K foot fall 5K children.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/30K foot fall 5K children.jpg');
  const snapshot200Animals = useResponsiveImage('200+ Animals 40+ Breeds.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/200+ Animals 40+ Breeds.jpg');
  const snapshot100Artists = useResponsiveImage('100+ Performance Artists.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/100+ Performance Artists.jpg');
  const snapshot20Exhibits = useResponsiveImage('20+ exhibits.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/20+ exhibits.jpg');
  const snapshot150Stalls = useResponsiveImage('150+ stalls.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/150+ stalls.jpg');
  const snapshot50kFootfall = useResponsiveImage('50k footfall 15k children.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/50k footfall 15k children.jpg');
  const snapshot250Animals = useResponsiveImage('250+ animals 40+ breads.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/250+ animals 40+ breads.jpg');
  const snapshot150Artists = useResponsiveImage('150+ performance artists.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/150+ performance artists.jpg');
  const snapshot25Exhibits = useResponsiveImage('25+ exhibits the land, it\'s history, the people.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/25+ exhibits the land, it\'s history, the people.jpg');
  const snapshot250Stalls = useResponsiveImage('250+ stalls.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/250+ stalls.jpg');
  const snapshot20Skills = useResponsiveImage('20+ Traditional Skills.jpg', IMAGE_CATEGORIES.SNAPSHOTS, 'homepage/snapshot/20+ Traditional Skills.jpg');

  // Responsive spotlight images
  const spotlightPicture1 = useResponsiveImage('Picture1.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/Picture1.png');
  const spotlightPicture2 = useResponsiveImage('Picture2.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/Picture2.png');
  const tvImage1 = useResponsiveImage('tv1.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/tv1.png');
  const tvImage2 = useResponsiveImage('tv2.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/tv2.png');
  const tvImage3 = useResponsiveImage('tv3.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/tv3.png');
  const tvImage4 = useResponsiveImage('tv4.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/tv4.png');
  const govtImage2 = useResponsiveImage('govt2.png', IMAGE_CATEGORIES.SPOTLIGHTS, 'homepage/spotlights/govt2.png');

  // Responsive ticket icon for pre-registration button
  const ticketIcon = useResponsiveImage('ticket-icon.png', IMAGE_CATEGORIES.ICONS, 'ticket-icon-original.png');

  // Gallery images for slideshow
  const galleryImages = [
    { src: cyclingImage, alt: "Cycling Activities" },
    { src: jumpingImage, alt: "Jumping Games" },
    { src: karthickImage, alt: "Karthick Performance" },
    { src: karupannanImage, alt: "Karupannan Traditional Art" },
    { src: kuththattamImage, alt: "Kuththattam Traditional Dance" },
    { src: melamImage, alt: "Traditional Melam" },
    { src: poikaalkuthiraiImage, alt: "Poikaal Kuthirai Dance" },
    { src: sakthiImage, alt: "Sakthi Performance" },
    { src: uralImage, alt: "Traditional Ural" },
    { src: uriImage, alt: "Uri Traditional Game" },
    { src: uriyadiImage, alt: "Uriyadi Festival Game" }
  ];

  const content = {
    english: {
      title: "Sempozhil 2025",
      // tagline: "There is a child in every adult,\nand a villager in every urbanite!",
      tagline: "",
      subtitle: "",
      dates: "August 21 – 24 | Aavani 05 – 08",
      venue: "YMCA Nandanam, Chennai",
      preRegistration: "Pre-Register Now",
      countdown: {
        title: "Event Starts In",
        days: "Days",
        hours: "Hours",
        minutes: "Minutes",
        seconds: "Seconds"
      },
      videoTitle: "",
      section1: {
        heading: "The Village Festival in the Heart of the City",
        description: "Sempozhil is a village festival themed event held every year in Chennai with the core vision of bringing traditional Tamil village culture to urban audiences. It aims to celebrate and promote the values of rural life, including farming, native livestock, folk arts, and traditional games, in a way that reconnects city dwellers with their cultural roots.\n\nIt's an immersive experience designed to reconnect everyone with timeless traditions of rural life, wisdom, ecological balance, and cultural pride, making it an unforgettable experience for children, families, youth, and cultural enthusiasts."
      },
      section2: {
        heading: "Cultural Heritage",
        description: "Experience the rich cultural heritage of Tamil Nadu through traditional arts, crafts, and performances. Our festival showcases authentic village traditions, local artisans, and time-honored practices that have been passed down through generations, creating a bridge between past and present."
      },
      snapshot2024: {
        heading: "Sempozhil 2024 - A Snapshot",
        cards: [
          {
            text: "30k+ Footfall<br/>5k Children",
            image: snapshot30kFootfall
          },
          {
            text: "200+ Animals<br/>40+ Breeds",
            image: snapshot200Animals
          },
          {
            text: "100+ Performance Artists",
            image: snapshot100Artists
          },
          {
            text: "20+ Exhibits<br/>The Land, its History, the People, their Traditions",
            image: snapshot20Exhibits
          },
          {
            text: "150+ Stalls<br/>Direct Farmers & MSME Enterprises",
            image: snapshot150Stalls
          }
        ]
      },
      snapshot2025: {
        heading: "Sempozhil 2025 - A Sneak Peek",
        cards: [
          {
            text: "20+ Traditional Skills<br/>Workshops & Contests",
            image: snapshot20Skills,
            isNew: true
          },
          {
            text: "50k+ Footfall<br/>15k Children",
            image: snapshot50kFootfall
          },
          {
            text: "250+ Animals<br/>40+ Breeds",
            image: snapshot250Animals
          },
          {
            text: "150+ Performance Artists",
            image: snapshot150Artists
          },
          {
            text: "25+ Exhibits<br/>The Land, its History, the People, their Traditions",
            image: snapshot25Exhibits
          },
          {
            text: "250+ Stalls<br/>Direct Farmers & MSME Enterprises",
            image: snapshot250Stalls
          }
        ]
      },
      recognition: {
        heading: "Sempozhil 2024 - In the Spotlight",
        media: [
          {
            type: "Print Media",
            outlets: ["The Hindu", "Times of India", "Deccan Chronicle"],
            images: [spotlightPicture1, spotlightPicture2]
          },
          { type: "TV Channels", outlets: ["Sun TV", "Vijay TV", "Zee Tamil"] },
          { type: "Government", outlets: ["Ministry of Culture", "Tamil Nadu Tourism"] }
        ]
      },
      preview2025: {
        heading: "Sempozhil 2025 - A Sneak Peek",
        features: [
          "Expanded village experiences",
          "International cultural exchange",
          "Digital heritage documentation",
          "Sustainable farming workshops"
        ]
      },
      testimonials: [
        {
          text: "The kids were full of excitement, curiosity, and joy as they explored the village type of set up and learned new things. It was amazing to see them connect with nature and the community, stepping out of their usual routine and gaining valuable insights about rural life.",
          author: "Nirmala Mohan, The Principal | CK groups of Schools | Guduvancheri"
        },
        {
          text: "The kids enjoyed visiting the event, also learnt about traditional values and culture.. the musical instruments expo, historical coins exhibit, heirloom seeds, wooden toys were one of a kind. The cattle and livestock expo was extraordinary. My colleagues who were from the city said that it is their first time visiting such event and totally a different experience from routine city life!!! I lifted the ilavatta Kal and got rewards. I never realized that I could lift 30 kgs...",
          author: "Rajalakshmi, Working Professional | Chennai"
        },
        {
          text: "We are glad to see the over whelming response from the public and the shop owners who like to add our Real sodas in their top selling product list. Thanks a lot for the Sempozhil organizing team for coining this kind of trend setting event to cater all conscious consumers , farmers and real producers. This event gave a great opening for our startup.",
          author: "Gayathri Devi and Illanthendral Neer enterprises | Chennai"
        },
        {
          text: "We are happy to be a part of Sempozhil and are thankful to the organizers for recognizing our art/craft and including us in this celebration, while giving us visibility and exposure to the larger audience.",
          author: "Anand & Vannichi Craftsmen | Ornaments for Jallikattu | Sivagangai"
        }
      ]
    },
    tamil: {
      title: "செம்பொழில் 2025",
      // tagline: "ஒவ்வொரு பெரியவரிலும் ஒரு குழந்தை இருக்கிறது.. மற்றும் ஒவ்வொரு நகரவாசியிலும் ஒரு கிராமவாசி இருக்கிறார்.",
      tagline: "",
      subtitle: "சென்னையின் பெரிய கிராம திருவிழா மற்றும் வர்த்தக கண்காட்சி",
      dates: "ஆகஸ்ட் 21 – 24 | ஆவணி 05 – 08",
      venue: "YMCA நந்தனம், சென்னை",
      preRegistration: "முன்பதிவு செய்யுங்கள்",
      countdown: {
        title: "நிகழ்வு தொடங்குவதற்கு",
        days: "நாட்கள்",
        hours: "மணி",
        minutes: "நிமிடங்கள்",
        seconds: "விநாடிகள்"
      },
      videoTitle: "",
      section1: {
        heading: "நகரத்தின் இதயத்தில் கிராம திருவிழா",
        description: "செம்பொழில் என்பது ஒவ்வொரு ஆண்டும் சென்னையில் நடைபெறும் ஒரு கிராம திருவிழா தீம் நிகழ்வாகும், இது பாரம்பரிய தமிழ் கிராம கலாச்சாரத்தை நகர்ப்புற பார்வையாளர்களுக்கு கொண்டு வருவதற்கான முக்கிய நோக்கத்துடன் உள்ளது. இது விவசாயம், பூர்வீக கால்நடைகள், நாட்டுப்புற கலைகள் மற்றும் பாரம்பரிய விளையாட்டுகள் உள்ளிட்ட கிராமப்புற வாழ்க்கையின் மதிப்புகளை கொண்டாடுவதையும் ஊக்குவிப்பதையும் நோக்கமாகக் கொண்டுள்ளது.\n\nஇது கிராமப்புற வாழ்க்கையின் காலமற்ற பாரம்பரியங்கள், ஞானம், சுற்றுச்சூழல் சமநிலை மற்றும் கலாச்சார பெருமையுடன் அனைவரையும் மீண்டும் இணைக்க வடிவமைக்கப்பட்ட ஒரு அனுபவமாகும்."
      },
      section2: {
        heading: "கலாச்சார பாரம்பரியம்",
        description: "பாரம்பரிய கலைகள், கைவினைப்பொருட்கள் மற்றும் நிகழ்ச்சிகள் மூலம் தமிழ்நாட்டின் வளமான கலாச்சார பாரம்பரியத்தை அனுபவிக்கவும். எங்கள் திருவிழா உண்மையான கிராம பாரம்பரியங்கள், உள்ளூர் கைவினைஞர்கள் மற்றும் தலைமுறைகளாக கடத்தப்பட்ட மரியாதைக்குரிய நடைமுறைகளை காட்சிப்படுத்துகிறது."
      },
      snapshot2024: {
        heading: "செம்பொழில் 2024 - ஒரு பார்வை",
        cards: [
          {
            text: "30k+ பார்வையாளர்கள்<br/>5k குழந்தைகள்",
            image: snapshot30kFootfall
          },
          {
            text: "200+ விலங்குகள்<br/>40+ இனங்கள்",
            image: snapshot200Animals
          },
          {
            text: "100+ கலைஞர்கள்",
            image: snapshot100Artists
          },
          {
            text: "20+ கண்காட்சிகள்<br/>நிலம், வரலாறு, மக்கள், பாரம்பரியம்",
            image: snapshot20Exhibits
          },
          {
            text: "150+ கடைகள்<br/>நேரடி விவசாயிகள் & MSME நிறுவனங்கள்",
            image: snapshot150Stalls
          }
        ]
      },
      snapshot2025: {
        heading: "செம்பொழில் 2025 - ஒரு முன்னோட்டம்",
        cards: [
          {
            text: "20+ பாரம்பரிய திறன்கள்<br/>பட்டறைகள் & போட்டிகள்",
            image: snapshot20Skills,
            isNew: true
          },
          {
            text: "50k+ பார்வையாளர்கள்<br/>15k குழந்தைகள்",
            image: snapshot50kFootfall
          },
          {
            text: "250+ விலங்குகள்<br/>40+ இனங்கள்",
            image: snapshot250Animals
          },
          {
            text: "150+ கலைஞர்கள்",
            image: snapshot150Artists
          },
          {
            text: "25+ கண்காட்சிகள்<br/>நிலம், வரலாறு, மக்கள், பாரம்பரியம்",
            image: snapshot25Exhibits
          },
          {
            text: "250+ கடைகள்<br/>நேரடி விவசாயிகள் & MSME நிறுவனங்கள்",
            image: snapshot250Stalls
          }
        ]
      },
      recognition: {
        heading: "செம்பொழில் 2024 - கவனத்தில்",
        media: [
          {
            type: "அச்சு ஊடகங்கள்",
            outlets: ["தி இந்து", "டைம்ஸ் ஆஃப் இந்தியா", "டெக்கான் க்ரானிக்கிள்"],
            images: [spotlightPicture1, spotlightPicture2]
          },
          { type: "தொலைக்காட்சி சேனல்கள்", outlets: ["சன் டிவி", "விஜய் டிவி", "ஜீ தமிழ்"] },
          { type: "அரசாங்கம்", outlets: ["கலாச்சார அமைச்சகம்", "தமிழ்நாடு சுற்றுலா"] }
        ]
      },
      preview2025: {
        heading: "செம்பொழில் 2025 - ஒரு முன்னோட்டம்",
        features: [
          "விரிவாக்கப்பட்ட கிராம அனுபவங்கள்",
          "சர்வதேச கலாச்சார பரிமாற்றம்",
          "டிஜிட்டல் பாரம்பரிய ஆவணப்படுத்தல்",
          "நிலையான விவசாய பட்டறைகள்"
        ]
      },
      testimonials: [
        {
          text: "குழந்தைகள் கிராமத்து சூழலை ஆராய்ந்து புதிய விஷயங்களைக் கற்றுக்கொண்டபோது அவர்களின் உற்சாகம், ஆர்வம் மற்றும் மகிழ்ச்சி நிறைந்திருந்தது.",
          author: "நிர்மலா மோகன், முதல்வர் | சி.கே குழுமம் பள்ளிகள் | குடுவாஞ்சேரி"
        },
        {
          text: "குழந்தைகள் நிகழ்வைப் பார்வையிட்டு மகிழ்ந்தனர், பாரம்பரிய மதிப்புகள் மற்றும் கலாச்சாரத்தைப் பற்றியும் கற்றுக்கொண்டனர். இசைக்கருவிகள் கண்காட்சி, வரலாற்று நாணயங்கள் கண்காட்சி அற்புதமாக இருந்தது.",
          author: "ராஜலட்சுமி, பணியாளர் | சென்னை"
        },
        {
          text: "பொதுமக்கள் மற்றும் கடை உரிமையாளர்களின் அபரிமிதமான வரவேற்பைக் கண்டு நாங்கள் மகிழ்ச்சியடைகிறோம். செம்பொழில் ஏற்பாட்டுக் குழுவிற்கு நன்றி.",
          author: "காயத்ரி தேவி மற்றும் இல்லந்தென்றல் நீர் நிறுவனங்கள் | சென்னை"
        },
        {
          text: "செம்பொழிலின் ஒரு பகுதியாக இருப்பதில் நாங்கள் மகிழ்ச்சியடைகிறோம் மற்றும் எங்கள் கலை/கைவினைப்பொருட்களை அங்கீகரித்து பெரிய பார்வையாளர்களுக்கு வெளிப்பாட்டை வழங்கியதற்காக ஏற்பாட்டாளர்களுக்கு நன்றியுள்ளவர்களாக இருக்கிறோம்.",
          author: "ஆனந்த் & வன்னிச்சி கைவினைஞர்கள் | ஜல்லிக்கட்டு ஆபரணங்கள் | சிவகங்கை"
        }
      ]
    }
  };

  const currentContent = content[language];

  // Countdown timer logic
  useEffect(() => {
    const targetDate = new Date('2025-08-21T09:00:00').getTime();

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const difference = targetDate - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Responsive logo logic
  useEffect(() => {
    const handleResize = () => {
      setCurrentLogo(getResponsiveLogo());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Seconds visibility animation - synchronized with countdown
  useEffect(() => {
    const visibilityTimer = setInterval(() => {
      setSecondsVisible(prev => !prev);
    }, 500); // Toggle every 500ms (hide for 500ms, show for 500ms)

    return () => clearInterval(visibilityTimer);
  }, []);

  // Auto-scroll slideshow
  useEffect(() => {
    const slideTimer = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % galleryImages.length);
    }, 4000); // Change slide every 4 seconds

    return () => clearInterval(slideTimer);
  }, [galleryImages.length]);

  // Video logic removed - now handled by OptimizedYouTubeEmbed component

  // Manual slide navigation
  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % galleryImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + galleryImages.length) % galleryImages.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const CountdownTimer = () => (
    <div className="countdown-wrapper">
      {/* Title above the golden border */}
      <div className="countdown-title-wrapper">
        <img src={instrumentIcon} alt="Instrument" className="countdown-title-icon" />
        <h3 className="countdown-title">{currentContent.countdown.title}</h3>
      </div>

      {/* Main countdown container with golden border */}
      <div className="countdown-container">
        {/* Left side - Date information */}
        <div className="countdown-left">
          <div className="countdown-dates">Aug 21-24 &nbsp;&nbsp;Aavani 5-8</div>
          <div className="countdown-tamil-dates"></div>
        </div>

        {/* Center - Timer section */}
        <div className="countdown-center">
          <div className="countdown-timer">
            {/* Top section - Numbers only */}
            <div className="countdown-numbers-section">
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.days}:</div>
              </div>
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.hours}:</div>
              </div>
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.minutes}:</div>
              </div>
              <div className="countdown-item countdown-seconds">
                <div
                  className="countdown-number"
                  style={{
                    opacity: secondsVisible ? 1 : 0,
                    visibility: secondsVisible ? 'visible' : 'hidden',
                    transition: 'opacity 0.1s ease-in-out'
                  }}
                >
                  {timeLeft.seconds}
                </div>
              </div>
            </div>

            {/* Horizontal divider line */}
            <div className="countdown-divider"></div>

            {/* Bottom section - Labels only */}
            <div className="countdown-labels-section">
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.days}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.hours}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.minutes}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.seconds}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Location information */}
        <div className="countdown-right">
          <div className="countdown-location">
            <img src={locationIcon} alt="Location" className="location-icon" />
            <div className="location-text">YMCA Nandanam, Chennai</div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="homepage-container">
      {/* Fixed Countdown Timer at Bottom */}
      <CountdownTimer />

      {/* Main content */}
      <div className="homepage-content">
        {/* Tagline section */}
        <div className="tagline-section">
          <p className="tagline">{currentContent.tagline}</p>
        </div>

        {/* Text content */}
        <div className="text-content">
          <div className="main-title">
            <img
              src={currentLogo}
              alt={language === 'tamil' ? 'செম்பொழில் 2025' : 'Sempozhil 2025'}
              className="title-logo-image"
            />
          </div>
          <div className="festival-tagline">
            India's Largest Urban Village Festival and Trade Expo
          </div>
          <p className="subtitle">{currentContent.subtitle}</p>

          {/* Desktop Pre-registration Button with Ticket Icon - Only visible on desktop */}
          <div className="desktop-preregister-main">
            <a
              href="https://docs.google.com/forms/d/e/1FAIpQLSd7gHhGbqktemsaRi0ewSyuMvMIDEl58ZNJ9EpTnw5B3pkK9g/viewform"
              target="_blank"
              rel="noopener noreferrer"
              className="preregister-button desktop-preregister-button ticket-icon-button"
            >
              <img src={ticketIcon} alt="Ticket Icon" className="ticket-icon-image" />
            </a>
          </div>

          {/* Mobile Date and Location - Only visible on mobile */}
          <div className="mobile-date-location">
            <div className="mobile-dates">Aug 21-24 &nbsp;&nbsp;Aavani 5-8</div>
            <div className="mobile-venue">YMCA Nandanam, Chennai</div>
            <div className="mobile-preregister">
              <a
                href="https://docs.google.com/forms/d/e/1FAIpQLSd7gHhGbqktemsaRi0ewSyuMvMIDEl58ZNJ9EpTnw5B3pkK9g/viewform"
                target="_blank"
                rel="noopener noreferrer"
                className="preregister-button ticket-icon-button"
              >
                <img src={ticketIcon} alt="Ticket Icon" className="ticket-icon-image" />
              </a>
            </div>
          </div>
        </div>

        {/* YouTube Video Embed - Moved to scrollable section */}
        <div className="video-section">
          <h3 className="video-title">{currentContent.videoTitle}</h3>
          <OptimizedYouTubeEmbed
            videoId="7hSr3b2qVck"
            title=""
            autoplay={false}
            muted={false}
            enableLazyLoad={true}
            showPlayButton={true}
            className="homepage-main-video"
            height="500px"
          />
        </div>

        {/* 2025 Event Snapshot Cards */}
        <div className="snapshot-section">
          <h2 className="section-heading">{currentContent.snapshot2025.heading}</h2>
          <div className="snapshot-cards-2025">
            {currentContent.snapshot2025.cards.map((card, index) => (
              <div key={index} className={`snapshot-image-card ${card.isNew ? 'new-feature' : ''}`}>
                {card.isNew && <div className="new-tag">NEW</div>}
                <div className="snapshot-image">
                  <img src={card.image} alt={`2025 Snapshot ${index + 1}`} />
                </div>
                <div className="snapshot-text" dangerouslySetInnerHTML={{ __html: card.text }}></div>
              </div>
            ))}
          </div>
        </div>

        {/* 2024 Event Snapshot Cards */}
        <div className="snapshot-section">
          <h2 className="section-heading">{currentContent.snapshot2024.heading}</h2>
          <div className="snapshot-cards-2024">
            <div className="snapshot-row-1">
              {currentContent.snapshot2024.cards.slice(0, 2).map((card, index) => (
                <div key={index} className="snapshot-image-card">
                  <div className="snapshot-image">
                    <img src={card.image} alt={`Snapshot ${index + 1}`} />
                  </div>
                  <div className="snapshot-text" dangerouslySetInnerHTML={{ __html: card.text }}></div>
                </div>
              ))}
            </div>
            <div className="snapshot-row-2">
              {currentContent.snapshot2024.cards.slice(2, 5).map((card, index) => (
                <div key={index + 2} className="snapshot-image-card">
                  <div className="snapshot-image">
                    <img src={card.image} alt={`Snapshot ${index + 3}`} />
                  </div>
                  <div className="snapshot-text" dangerouslySetInnerHTML={{ __html: card.text }}></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Event Scope & Vision Section */}
        <div className="vision-section">
          <div className="vision-content">
            <div className="vision-main-row">
              <div className="vision-main-image">
                <img src={kuththattamImage} alt="Sempozhil Festival Main Event" className="main-layout-img" />
              </div>
              <div className="vision-text-content">
                <h2 className="layout-heading">{currentContent.section1.heading}</h2>
                <p className="layout-description">{currentContent.section1.description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Photo Slideshow */}
        <div className="slideshow-section">
          <div className="slideshow-container">
            <div className="slideshow-wrapper" style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
              {galleryImages.map((image, index) => (
                <div key={index} className="slide">
                  <img src={image.src} alt={image.alt} className="slide-image" />
                </div>
              ))}
            </div>

            {/* Navigation buttons */}
            <button className="slide-nav prev" onClick={prevSlide}>‹</button>
            <button className="slide-nav next" onClick={nextSlide}>›</button>

            {/* Dots indicator */}
            <div className="slide-dots">
              {galleryImages.map((_, index) => (
                <button
                  key={index}
                  className={`dot ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Spotlight Section */}
        <div className="spotlight-section">
          <h2 className="section-heading">{currentContent.recognition.heading}</h2>

          {/* Task 1: Print Media Section */}
          <div className="print-media-section">
            <h3 className="spotlight-subsection-title">
              {language === 'tamil' ? 'அச்சு ஊடகங்கள்' : 'Print Media'}
            </h3>
            <div className="print-media-images-fullwidth">
              <a
                href="https://www.newindianexpress.com/cities/chennai/2024/Sep/30/vestiges-from-the-villages"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img
                  src={spotlightPicture1}
                  alt="Print Media Coverage 1"
                  className="print-media-image-fullwidth"
                />
              </a>
              <a
                href="https://www.dailythanthi.com/Cinema/CinemaNews/sempozhil-village-festival-introduce-tamil-heritage-to-kids-actor-karthi-1124244"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img
                  src={spotlightPicture2}
                  alt="Print Media Coverage 2"
                  className="print-media-image-fullwidth"
                />
              </a>
            </div>
          </div>

          {/* Task 2: TV Channels Section - Card-based Layout */}
          <div className="tv-channels-section">
            <h3 className="spotlight-subsection-title">
              {language === 'tamil' ? 'தொலைக்காட்சி சேனல்கள்' : 'TV Channels'}
            </h3>
            <div className="tv-channels-cards-grid">
              <div className="tv-channel-card">
                <a
                  href="https://www.youtube.com/watch?v=d5EkiQS18Rw"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="tv-channel-link"
                >
                  <div className="tv-channel-image-container">
                    <img src={tvImage1} alt="TV Channel Coverage 1" className="tv-channel-card-image" />
                  </div>
                </a>
              </div>
              <div className="tv-channel-card">
                <a
                  href="https://www.youtube.com/watch?v=LfezHBOSqbk"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="tv-channel-link"
                >
                  <div className="tv-channel-image-container">
                    <img src={tvImage2} alt="TV Channel Coverage 2" className="tv-channel-card-image" />
                  </div>
                </a>
              </div>
              <div className="tv-channel-card">
                <a
                  href="https://www.youtube.com/watch?v=PzcNaJQCGrA"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="tv-channel-link"
                >
                  <div className="tv-channel-image-container">
                    <img src={tvImage3} alt="TV Channel Coverage 3" className="tv-channel-card-image" />
                  </div>
                </a>
              </div>
              <div className="tv-channel-card">
                <a
                  href="https://www.youtube.com/watch?v=LWElT_9OrRU"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="tv-channel-link"
                >
                  <div className="tv-channel-image-container">
                    <img src={tvImage4} alt="TV Channel Coverage 4" className="tv-channel-card-image" />
                  </div>
                </a>
              </div>
            </div>
          </div>

          {/* Task 3: Government Section - Card-based Layout */}
          <div className="government-section">
            <h3 className="spotlight-subsection-title">
              {language === 'tamil' ? 'சுற்றுலா அமைச்சகம்' : 'Ministry of Tourism'}
            </h3>
            <div className="government-cards-grid">
              <div className="government-card">
                <div
                  className="government-card-content"
                  onClick={() => window.open('https://utsav.gov.in/view-event/sempozhil-a-village-festival-in-chennai', '_blank')}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="government-image-container">
                    <img
                      src={govtImage2}
                      alt="Government Recognition - Ministry of Tourism"
                      className="government-card-image"
                    />
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Testimonials Section */}
        <div className="testimonials-section">
          <h2 className="section-heading">What People Say</h2>
          <div className="testimonials-grid">
            {currentContent.testimonials.map((testimonial, index) => (
              <div key={index} className="testimonial-card">
                <p className="testimonial-text">"{testimonial.text}"</p>
                <p className="testimonial-author">- {testimonial.author}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Homepage;
